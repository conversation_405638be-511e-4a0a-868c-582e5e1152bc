/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { ApplicationCommandOptionType } from 'discord.js';
import BaseCommand from '#src/core/BaseCommand.js';
import BanManager from '#src/managers/UserBanManager.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import Logger from '#src/utils/Logger.js';
import type Context from '#src/core/CommandContext/Context.js';

export default class Unban extends BaseCommand {
  constructor() {
    super({
      name: 'unban',
      description: '🔓 Unban a user from using the bot. (Staff Only)',
      staffOnly: true,
      types: { slash: true, prefix: true },
      options: [
        {
          type: ApplicationCommandOptionType.User,
          name: 'user',
          description: '🔓 The user to unban.',
          required: false,
        },
        {
          type: ApplicationCommandOptionType.String,
          name: 'ban_id',
          description: 'The ban ID to revoke (alternative to user)',
          required: false,
        },
      ],
    });
  }

  async execute(ctx: Context) {
    const user = await ctx.options.getUser('user');
    const banId = ctx.options.getString('ban_id');

    if (!user && !banId) {
      await ctx.reply({
        content: 'You must provide either a user or a ban ID.',
        flags: ['Ephemeral'],
      });
      return;
    }

    const banManager = new BanManager();

    try {
      if (banId) {
        // Unban by ban ID
        const ban = await banManager.revokeBan(banId, ctx.user.id);

        Logger.info(`Ban ${banId} revoked by ${ctx.user.username} (${ctx.user.id})`);

        await ctx.reply(
          `${getEmoji('tick', ctx.client)} Successfully revoked ban \`${banId}\` for user <@${ban.userId}>.`,
        );
      }
      else if (user) {
        // Unban by user
        const banCheck = await banManager.isUserBanned(user.id);

        if (!banCheck.isBanned || !banCheck.ban) {
          await ctx.reply({
            content: `${getEmoji('info_icon', ctx.client)} User ${user.username} is not currently banned.`,
            flags: ['Ephemeral'],
          });
          return;
        }

        const ban = await banManager.revokeBan(banCheck.ban.id, ctx.user.id);

        Logger.info(`User ${user.username} (${user.id}) unbanned by ${ctx.user.username} (Ban ID: ${ban.id})`);

        await ctx.reply(
          `${getEmoji('tick', ctx.client)} Successfully unbanned \`${user.username}\`. They can now use the bot again. (Ban ID: \`${ban.id}\`)`,
        );
      }
    }
    catch (error) {
      Logger.error('Error revoking ban:', error);
      await ctx.reply({
        content: `${getEmoji('x_icon', ctx.client)} Failed to unban: ${error instanceof Error ? error.message : 'Unknown error'}`,
        flags: ['Ephemeral'],
      });
    }
  }
}
