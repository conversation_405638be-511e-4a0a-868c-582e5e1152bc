/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import { handleBan } from '#utils/BanUtils.js';
import { ApplicationCommandOptionType } from 'discord.js';
import ms from 'ms';

export default class Ban extends BaseCommand {
  constructor() {
    super({
      name: 'ban',
      description: '🔨 Ban a user from using the bot. (Dev Only)',
      staffOnly: true,
      types: { slash: true, prefix: true },
      options: [
        {
          type: ApplicationCommandOptionType.User,
          name: 'user',
          description: '🔨 The user to ban.',
          required: true,
        },
        {
          type: ApplicationCommandOptionType.String,
          name: 'reason',
          description: 'Reason for the ban',
          required: true,
        },
        {
          type: ApplicationCommandOptionType.String,
          name: 'type',
          description: 'Type of ban (permanent or temporary)',
          required: false,
          choices: [
            { name: 'Permanent', value: 'PERMANENT' },
            { name: 'Temporary', value: 'TEMPORARY' },
          ],
        },
        {
          type: ApplicationCommandOptionType.String,
          name: 'duration',
          description: 'Duration for temporary bans (e.g., 1h, 1d, 1w)',
          required: false,
        },
      ],
    });
  }

  async execute(ctx: Context) {
    const user = await ctx.options.getUser('user');
    const reason = ctx.options.getString('reason', true);
    const banType = (ctx.options.getString('type') as 'PERMANENT' | 'TEMPORARY') || 'PERMANENT';
    const durationStr = ctx.options.getString('duration');

    if (!user) {
      await ctx.reply('User not found');
      return;
    }

    // Parse duration for temporary bans
    let duration: number | undefined;
    if (banType === 'TEMPORARY') {
      if (!durationStr) {
        await ctx.reply({
          content: 'Duration is required for temporary bans. Use format like: 1h, 1d, 1w, 30d',
          flags: ['Ephemeral'],
        });
        return;
      }

      try {
        duration = ms(durationStr as ms.StringValue);
      }
      catch {
        if (!duration) {
          await ctx.reply({
            content: 'Invalid duration format. Use format like: 1h, 1d, 1w, 30d',
            flags: ['Ephemeral'],
          });
          return;
        }
      }
    }

    await handleBan(ctx, user.id, user, reason, banType, duration);
  }
}
