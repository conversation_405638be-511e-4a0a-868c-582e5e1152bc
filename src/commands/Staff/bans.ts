/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import BanManager from '#src/managers/UserBanManager.js';
import ServerBanManager from '#src/managers/ServerBanManager.js';
import { UIComponents } from '#src/utils/DesignSystem.js';
import { CustomID } from '#src/utils/CustomID.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import Logger from '#src/utils/Logger.js';
import { PaginationManager } from '#src/utils/ui/PaginationManager.js';
import {
  ApplicationCommandOptionType,
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  MessageFlags,
} from 'discord.js';

export default class Bans extends BaseCommand {
  private client: any;

  constructor() {
    super({
      name: 'bans',
      description: '📋 View and manage all active bans with filtering and search options',
      staffOnly: true,
      types: { slash: true },
      options: [
        {
          type: ApplicationCommandOptionType.String,
          name: 'filter',
          description: 'Filter bans by type',
          required: false,
          choices: [
            { name: '👤 User Bans Only', value: 'user' },
            { name: '🌐 Server Bans Only', value: 'server' },
            { name: '♾️ Permanent Bans', value: 'permanent' },
            { name: '⏰ Temporary Bans', value: 'temporary' },
            { name: '📊 All Bans', value: 'all' },
          ],
        },
        {
          type: ApplicationCommandOptionType.String,
          name: 'search',
          description: 'Search bans by username, server name, reason, or moderator',
          required: false,
        },
      ],
    });
  }

  async execute(ctx: Context) {
    this.client = ctx.client; // Set client for use in pagination
    const filter = ctx.options.getString('filter') || 'all';
    const search = ctx.options.getString('search');

    try {
      // Get all active bans
      const banManager = new BanManager();
      const serverBanManager = new ServerBanManager();

      const [userBans, serverBans] = await Promise.all([
        banManager.getActiveBans(),
        serverBanManager.getActiveBans(),
      ]);

      // Combine and filter bans
      const allBans: Array<{ type: 'user' | 'server'; data: any; displayName: string; moderatorName: string }> = [];

      // Process user bans
      if (filter === 'all' || filter === 'user' || filter === 'permanent' || filter === 'temporary') {
        for (const ban of userBans) {
          if (filter === 'permanent' && ban.type !== 'PERMANENT') continue;
          if (filter === 'temporary' && ban.type !== 'TEMPORARY') continue;

          const user = await ctx.client.users.fetch(ban.userId).catch(() => null);
          const moderator = await ctx.client.users.fetch(ban.moderatorId).catch(() => null);
          const displayName = user?.username || `Unknown User (${ban.userId})`;
          const moderatorName = moderator?.username || 'Unknown';

          // Apply search filter
          if (search) {
            const searchLower = search.toLowerCase();
            const matches = displayName.toLowerCase().includes(searchLower) ||
              ban.reason.toLowerCase().includes(searchLower) ||
              moderatorName.toLowerCase().includes(searchLower) ||
              ban.id.toLowerCase().includes(searchLower);

            if (!matches) continue;
          }

          allBans.push({ type: 'user', data: ban, displayName, moderatorName });
        }
      }

      // Process server bans
      if (filter === 'all' || filter === 'server' || filter === 'permanent' || filter === 'temporary') {
        for (const ban of serverBans) {
          if (filter === 'permanent' && ban.type !== 'PERMANENT') continue;
          if (filter === 'temporary' && ban.type !== 'TEMPORARY') continue;

          const server = await ctx.client.guilds.fetch(ban.serverId).catch(() => null);
          const moderator = await ctx.client.users.fetch(ban.moderatorId).catch(() => null);
          const displayName = server?.name || `Unknown Server (${ban.serverId})`;
          const moderatorName = moderator?.username || 'Unknown';

          // Apply search filter
          if (search) {
            const searchLower = search.toLowerCase();
            const matches = displayName.toLowerCase().includes(searchLower) ||
              ban.reason.toLowerCase().includes(searchLower) ||
              moderatorName.toLowerCase().includes(searchLower) ||
              ban.id.toLowerCase().includes(searchLower);

            if (!matches) continue;
          }

          allBans.push({ type: 'server', data: ban, displayName, moderatorName });
        }
      }

      // Sort by creation date (newest first)
      allBans.sort((a, b) => {
        const aDate = new Date(a.data.createdAt);
        const bDate = new Date(b.data.createdAt);
        return bDate.getTime() - aDate.getTime();
      });

      if (allBans.length === 0) {
        const ui = new UIComponents(ctx.client);
        const container = ui.createInfoMessage(
          'No Bans Found',
          search
            ? `No bans found matching your search criteria: "${search}"`
            : 'No active bans found with the selected filter.',
        );

        await ctx.reply({
          components: [container],
          flags: [MessageFlags.IsComponentsV2, 'Ephemeral'],
        });
        return;
      }

      // Create pagination
      const pagination = new PaginationManager({
        client: ctx.client,
        identifier: 'bans_list',
        items: allBans,
        itemsPerPage: 5,
        contentGenerator: this.generateBanListPage.bind(this),
        ephemeral: true,
      });

      await pagination.start(ctx);
    }
    catch (error) {
      Logger.error('Error in bans command:', error);
      await ctx.reply({
        content: `${getEmoji('x_icon', ctx.client)} Failed to load bans: ${error instanceof Error ? error.message : 'Unknown error'}`,
        flags: ['Ephemeral'],
      });
    }
  }

  /**
   * Generate a page for the ban list
   */
  private generateBanListPage(
    pageIndex: number,
    items: Array<{ type: 'user' | 'server'; data: any; displayName: string; moderatorName: string }>,
    totalPages: number,
    totalItems: number,
  ): ContainerBuilder {
    const ui = new UIComponents(this.client);
    const container = new ContainerBuilder();

    // Add header
    container.addTextDisplayComponents(
      ui.createHeader(
        'Active Bans',
        `Page ${pageIndex + 1} of ${totalPages} • ${totalItems} total bans`,
        'hammer_icon',
      ),
    );

    // Add ban entries
    for (const [index, ban] of items.entries()) {
      const banNumber = pageIndex * 5 + index + 1;
      const durationText = this.formatBanDuration(ban.data);
      const emoji = ban.type === 'user' ? '👤' : '🌐';
      const banTypeText = ban.type === 'user' ? 'User Ban' : 'Server Ban';

      const banInfo = `**${banNumber}. ${emoji} ${ban.displayName}**
**Type:** ${banTypeText} • **Duration:** ${durationText}
**Reason:** ${ban.data.reason.slice(0, 100)}${ban.data.reason.length > 100 ? '...' : ''}
**Moderator:** ${ban.moderatorName} • **ID:** \`${ban.data.id}\`
**Issued:** <t:${Math.floor(new Date(ban.data.createdAt).getTime() / 1000)}:R>`;

      container.addTextDisplayComponents(
        ui.createSubsection('', banInfo),
      );
    }

    return container;
  }

  /**
   * Format ban duration for display
   */
  private formatBanDuration(ban: any): string {
    if (ban.type === 'PERMANENT') return 'Permanent';
    if (ban.expiresAt) {
      const now = new Date();
      const expires = new Date(ban.expiresAt);
      if (expires <= now) return 'Expired';

      const diff = expires.getTime() - now.getTime();
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (days > 0) return `${days}d ${hours}h remaining`;
      return `${hours}h remaining`;
    }
    return 'Unknown';
  }
}
