import type { Ban, BanType } from '#src/generated/prisma/client/client.js';
import { CacheManager } from '#src/managers/CacheManager.js';
import { ConvertDatesToString } from '#src/types/Utils.js';
import Logger from '#src/utils/Logger.js';
import { getRedis } from '#src/utils/Redis.js';

export interface ServerBan extends Ban {
  serverId: string;
}

export interface CreateServerBanOptions {
  serverId: string;
  moderatorId: string;
  reason: string;
  type: BanType;
  duration?: number; // Duration in milliseconds for temporary bans
}

export interface ServerBanCheckResult {
  isBanned: boolean;
  ban?: ServerBan;
}

/**
 * Manager for handling server-level bans across InterChat
 * Similar to BanManager but for entire Discord servers
 */
export default class ServerBanManager {
  private readonly cacheManager: CacheManager;
  private static readonly CACHE_TTL = 300; // 5 minutes

  constructor() {
    this.cacheManager = new CacheManager(getRedis(), {
      prefix: 'server_ban',
      expirationMs: ServerBanManager.CACHE_TTL,
    });
  }

  /**
   * Create a new server ban
   */
  async createServerBan(options: CreateServerBanOptions): Promise<ServerBan> {
    const { serverId, moderatorId, reason, type, duration } = options;

    // Calculate expiration date for temporary bans
    const expiresAt = type === 'TEMPORARY' && duration ? new Date(Date.now() + duration) : null;

    // Check if server already has an active ban
    const existingBan = await this.getActiveServerBan(serverId);
    if (existingBan) {
      throw new Error(`Server ${serverId} is already banned (Ban ID: ${existingBan.id})`);
    }

    // Create the server ban record in Redis (since we don't have a ServerBan model in the schema)
    const banId = this.generateBanId();
    const serverBan: ServerBan = {
      id: banId,
      serverId,
      moderatorId,
      reason,
      type,
      duration,
      expiresAt: expiresAt || undefined,
      status: 'ACTIVE',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store in Redis
    const redis = getRedis();
    const banKey = `server_ban:${serverId}`;
    await redis.set(banKey, JSON.stringify(serverBan));

    // Set expiration if it's a temporary ban
    if (expiresAt) {
      await redis.expireat(banKey, Math.floor(expiresAt.getTime() / 1000));
    }

    // Invalidate cache
    await this.invalidateServerCache(serverId);

    Logger.info(`Server ${serverId} banned by ${moderatorId} (Ban ID: ${banId})`);

    return serverBan;
  }

  /**
   * Check if a server is currently banned
   */
  async isServerBanned(serverId: string): Promise<ServerBanCheckResult> {
    const cacheKey = `server:${serverId}`;

    // Try to get from cache first
    const cachedResult = await this.cacheManager.get<ServerBanCheckResult>(cacheKey);
    if (cachedResult) {
      return {
        ...cachedResult,
        ban: cachedResult.ban ? this.convertServerBanDates(cachedResult.ban) : undefined,
      };
    }

    // Get active ban from Redis
    const ban = await this.getActiveServerBan(serverId);
    const result: ServerBanCheckResult = {
      isBanned: !!ban,
      ban: ban || undefined,
    };

    // Cache the result
    await this.cacheManager.set(cacheKey, result);

    return result;
  }

  /**
   * Get active server ban
   */
  private async getActiveServerBan(serverId: string): Promise<ServerBan | null> {
    const redis = getRedis();
    const banKey = `server_ban:${serverId}`;
    const banData = await redis.get(banKey);

    if (!banData) return null;

    try {
      const ban = JSON.parse(banData) as ConvertDatesToString<ServerBan>;

      // Check if ban has expired
      if (ban.expiresAt && new Date(ban.expiresAt) <= new Date()) {
        // Ban has expired, remove it
        await redis.del(banKey);
        await this.invalidateServerCache(serverId);
        return null;
      }

      return this.convertServerBanDates(ban);
    }
    catch (error) {
      Logger.error('Error parsing server ban data:', error);
      return null;
    }
  }

  /**
   * Revoke a server ban
   */
  async revokeServerBan(serverId: string): Promise<boolean> {
    const redis = getRedis();
    const banKey = `server_ban:${serverId}`;
    const banData = await redis.get(banKey);

    if (!banData) return false;

    try {
      const ban = JSON.parse(banData) as ServerBan;
      ban.status = 'REVOKED';
      ban.updatedAt = new Date();

      await redis.set(banKey, JSON.stringify(ban));
      await this.invalidateServerCache(serverId);

      Logger.info(`Server ban revoked for ${serverId} (Ban ID: ${ban.id})`);
      return true;
    }
    catch (error) {
      Logger.error('Error revoking server ban:', error);
      return false;
    }
  }

  /**
   * Invalidate cache for a server
   */
  private async invalidateServerCache(serverId: string): Promise<void> {
    const cacheKey = `server:${serverId}`;
    await this.cacheManager.delete(cacheKey);
  }

  /**
   * Convert date strings back to Date objects
   */
  private convertServerBanDates(ban: ConvertDatesToString<ServerBan>): ServerBan {
    return {
      ...ban,
      createdAt: new Date(ban.createdAt),
      updatedAt: new Date(ban.updatedAt),
      expiresAt: ban.expiresAt ? new Date(ban.expiresAt) : undefined,
    };
  }

  /**
   * Generate a unique ban ID
   */
  private generateBanId(): string {
    return `sb_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Get all active server bans (for autocomplete and listing)
   */
  async getActiveBans(): Promise<ServerBan[]> {
    const redis = getRedis();
    const pattern = 'server_ban:*';
    const keys = await redis.keys(pattern);

    const activeBans: ServerBan[] = [];

    for (const key of keys) {
      const banData = await redis.get(key);
      if (!banData) continue;

      try {
        const banData_parsed = JSON.parse(banData) as ConvertDatesToString<ServerBan>;

        // Check if ban is still active
        if (banData_parsed.status === 'ACTIVE') {
          // Check if temporary ban hasn't expired
          if (banData_parsed.type === 'PERMANENT' || !banData_parsed.expiresAt || new Date(banData_parsed.expiresAt) > new Date()) {
            activeBans.push(this.convertServerBanDates(banData_parsed));
          }
        }
      }
      catch (error) {
        Logger.error('Error parsing server ban data:', error);
      }
    }

    // Sort by creation date (newest first)
    return activeBans.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()).slice(0, 100);
  }

  /**
   * Get a server ban by its ID
   */
  async getBanById(banId: string): Promise<ServerBan | null> {
    const redis = getRedis();
    const pattern = 'server_ban:*';
    const keys = await redis.keys(pattern);

    for (const key of keys) {
      const banData = await redis.get(key);
      if (!banData) continue;

      try {
        const banData_parsed = JSON.parse(banData) as ConvertDatesToString<ServerBan>;
        if (banData_parsed.id === banId) {
          return this.convertServerBanDates(banData_parsed);
        }
      }
      catch (error) {
        Logger.error('Error parsing server ban data:', error);
      }
    }

    return null;
  }

  /**
   * Revoke a server ban by ban ID
   */
  async revokeBan(banId: string, moderatorId: string): Promise<ServerBan> {
    const ban = await this.getBanById(banId);
    if (!ban) {
      throw new Error(`Server ban with ID ${banId} not found`);
    }

    if (ban.status !== 'ACTIVE') {
      throw new Error(`Server ban ${banId} is not active (status: ${ban.status})`);
    }

    // Update ban status
    ban.status = 'REVOKED';
    ban.updatedAt = new Date();

    // Store updated ban
    const redis = getRedis();
    const banKey = `server_ban:${ban.serverId}`;
    await redis.set(banKey, JSON.stringify(ban));

    // Invalidate cache
    await this.invalidateServerCache(ban.serverId);

    Logger.info(`Server ban revoked: ${banId} for server ${ban.serverId} by moderator ${moderatorId}`);

    return ban;
  }

  /**
   * Expire temporary server bans that have reached their expiration time
   * This method should be called by a scheduled task
   */
  async expireTemporaryServerBans(): Promise<number> {
    const redis = getRedis();
    const pattern = 'server_ban:*';
    const keys = await redis.keys(pattern);

    let expiredCount = 0;

    for (const key of keys) {
      const banData = await redis.get(key);
      if (!banData) continue;

      try {
        const ban = JSON.parse(banData) as ServerBan;

        if (ban.type === 'TEMPORARY' && ban.expiresAt && new Date(ban.expiresAt) <= new Date()) {
          await redis.del(key);
          const serverId = key.replace('server_ban:', '');
          await this.invalidateServerCache(serverId);
          expiredCount++;
        }
      }
      catch (error) {
        Logger.error('Error processing server ban expiration:', error);
      }
    }

    if (expiredCount > 0) {
      Logger.info(`Expired ${expiredCount} temporary server bans`);
    }

    return expiredCount;
  }
}
