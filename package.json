{"name": "interchat", "private": true, "version": "5.0.0", "description": "A growing Discord bot which provides inter-server chat!", "main": "build/index.js", "license": "AGPL-3.0-only", "scripts": {"start": "node .", "start:prod": "pm2 start .ecosystem.config.js", "dev": "nodemon --exec \"bun run build && bun start\" --ext ts,js,json --ignore build/", "build": "tsc --build", "typecheck": "tsc --noEmit", "locale-types": "bun scripts/genLocaleTypes.js", "sync:commands": "bun scripts/syncCommands.js", "sync:emojis": "bun scripts/syncEmojis.js", "release": "release-it", "lint": "eslint --cache --fix ./src", "prepare": "husky"}, "sponsor": {"url": "https://ko-fi.com/interchat"}, "type": "module", "dependencies": {"@hono/node-server": "^1.14.3", "@hono/zod-validator": "^0.5.0", "@prisma/client": "^6.8.2", "@sentry/node": "^9.22.0", "canvas": "^3.1.0", "common-tags": "^1.8.2", "discord-hybrid-sharding": "^2.2.6", "discord.js": "^14.19.3", "dotenv": "^16.5.0", "hono": "^4.7.10", "husky": "^9.1.7", "ioredis": "^5.6.1", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "lz-string": "^1.5.0", "ms": "^2.1.3", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "winston": "^3.17.0", "zod": "^3.25.28"}, "devDependencies": {"@stylistic/eslint-plugin": "^4.4.0", "@types/common-tags": "^1.8.4", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.17", "@types/ms": "^2.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.27.0", "lint-staged": "^16.0.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "prisma": "^6.8.2", "release-it": "^19.0.2", "source-map-support": "^0.5.21", "typescript": "5.8.3", "typescript-eslint": "^8.32.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.ts": ["eslint --cache --fix"]}, "imports": {"#src/*.js": "./build/*.js", "#utils/*.js": "./build/utils/*.js"}}